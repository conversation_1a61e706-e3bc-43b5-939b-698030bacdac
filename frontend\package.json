{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@heroicons/react": "^2.2.0", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "axios": "^1.9.0", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.5.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}